"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/ai-input */ \"(app-pages-browser)/./src/components/ui/ai-input.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation/navigation */ \"(app-pages-browser)/./src/components/Navigation/navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Page = ()=>{\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isAiTyping, setIsAiTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const generateChatTitle = (messages)=>{\n        if (messages.length === 0) return \"New Chat\";\n        const firstUserMessage = messages.find((msg)=>!msg.isAi);\n        if (firstUserMessage) {\n            return firstUserMessage.text.length > 30 ? firstUserMessage.text.substring(0, 30) + \"...\" : firstUserMessage.text;\n        }\n        return \"New Chat\";\n    };\n    const handleNewChat = ()=>{\n        // Save current chat to history if it has messages\n        if (messages.length > 0) {\n            const newChat = {\n                id: Date.now().toString(),\n                title: generateChatTitle(messages),\n                messages: [\n                    ...messages\n                ],\n                timestamp: new Date()\n            };\n            setChatHistory((prev)=>[\n                    newChat,\n                    ...prev\n                ]);\n        }\n        // Clear current chat\n        setMessages([]);\n        setCurrentChatId(null);\n        setIsAiTyping(false);\n    };\n    const handleLoadChat = (chat)=>{\n        // Save current chat if it has messages and is different from the one being loaded\n        if (messages.length > 0 && currentChatId !== chat.id) {\n            const currentChat = {\n                id: currentChatId || Date.now().toString(),\n                title: generateChatTitle(messages),\n                messages: [\n                    ...messages\n                ],\n                timestamp: new Date()\n            };\n            setChatHistory((prev)=>{\n                const filtered = prev.filter((c)=>c.id !== currentChatId);\n                return [\n                    currentChat,\n                    ...filtered\n                ];\n            });\n        }\n        // Load selected chat\n        setMessages(chat.messages);\n        setCurrentChatId(chat.id);\n        setIsAiTyping(false);\n    };\n    const handleMessage = async (message)=>{\n        // Add user message\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: message,\n                    isAi: false\n                }\n            ]);\n        setIsAiTyping(true);\n        // Simulate AI response (replace this with actual AI integration)\n        setTimeout(()=>{\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"This is a simulated AI response.\",\n                        isAi: true\n                    }\n                ]);\n            setIsAiTyping(false);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[250px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onNewChat: handleNewChat,\n                    onLoadChat: handleLoadChat,\n                    chatHistory: chatHistory,\n                    currentChatId: currentChatId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col h-[690px] relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-y-auto px-4 pb-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4 py-4\",\n                            children: [\n                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg max-w-[80%] \".concat(msg.isAi ? 'bg-neutral-100 self-start' : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'),\n                                        children: msg.text\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                isAiTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-neutral-100 self-start\",\n                                    children: \"AI is typing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-6 left-0 right-0 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onSendMessage: handleMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Page, \"f9toegg2CiUbQzDOs0vKWP6EREs=\");\n_c = Page;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});