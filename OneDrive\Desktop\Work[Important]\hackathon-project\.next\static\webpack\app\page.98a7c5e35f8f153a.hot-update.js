"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/ai-input */ \"(app-pages-browser)/./src/components/ui/ai-input.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation/navigation */ \"(app-pages-browser)/./src/components/Navigation/navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Page = ()=>{\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isAiTyping, setIsAiTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const handleMessage = async (message)=>{\n        // Add user message\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: message,\n                    isAi: false\n                }\n            ]);\n        setIsAiTyping(true);\n        // Simulate AI response (replace this with actual AI integration)\n        setTimeout(()=>{\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"This is a simulated AI response.\",\n                        isAi: true\n                    }\n                ]);\n            setIsAiTyping(false);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[250px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col h-[690px] relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-y-auto px-4 pb-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4 py-4\",\n                            children: [\n                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg max-w-[80%] \".concat(msg.isAi ? 'bg-neutral-100 self-start' : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'),\n                                        children: msg.text\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                isAiTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-neutral-100 self-start\",\n                                    children: \"AI is typing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-6 left-0 right-0 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onSendMessage: handleMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Page, \"f9toegg2CiUbQzDOs0vKWP6EREs=\");\n_c = Page;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});