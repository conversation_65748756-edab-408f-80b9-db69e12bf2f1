"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/message-square.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MessageSquare)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z\",\n            key: \"18887p\"\n        }\n    ]\n];\nconst MessageSquare = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"message-square\", __iconNode);\n //# sourceMappingURL=message-square.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navigation/navigation.tsx":
/*!**************************************************!*\
  !*** ./src/components/Navigation/navigation.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SidebarNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageSquare,SquareArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageSquare,SquareArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,MessageSquare,SquareArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SidebarNavigation(param) {\n    let { onNewChat, onLoadChat, chatHistory, currentChatId } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const toggleSidebar = ()=>setIsOpen(true);\n    const closeSidebar = ()=>setIsOpen(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const startNewChat = ()=>{\n        onNewChat();\n        closeSidebar();\n    };\n    const formatTimestamp = (timestamp)=>{\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days === 0) {\n            return 'Today';\n        } else if (days === 1) {\n            return 'Yesterday';\n        } else if (days < 7) {\n            return \"\".concat(days, \" days ago\");\n        } else {\n            return timestamp.toLocaleDateString();\n        }\n    };\n    // Close on outside click\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarNavigation.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {\n                    closeSidebar();\n                }\n            }\n            if (isOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n            } else {\n                document.removeEventListener('mousedown', handleClickOutside);\n            }\n            return ({\n                \"SidebarNavigation.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"SidebarNavigation.useEffect\"];\n        }\n    }[\"SidebarNavigation.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: sidebarRef,\n                className: \"fixed top-0 left-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 \".concat(isOpen ? 'translate-x-0' : '-translate-x-full'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeSidebar,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 cursor-pointer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"default\",\n                            className: \"w-full cursor-pointer mb-4\",\n                            onClick: startNewChat,\n                            children: \"+ New Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-sm font-semibold text-gray-600 mb-2\",\n                                    children: \"Previous Chats\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>{\n                                                    onLoadChat(chat);\n                                                    closeSidebar();\n                                                },\n                                                className: \"p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-100 \".concat(currentChatId === chat.id ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4 mt-0.5 text-gray-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                    children: chat.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 45\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: formatTimestamp(chat.timestamp)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, chat.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 33\n                                            }, this)),\n                                        chatHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 text-center py-4\",\n                                            children: \"No previous chats\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                lineNumber: 87,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"absolute top-4 left-4 z-60 bg-white p-2 rounded-full drop-shadow-lg shadow-black transition-opacity duration-300 \".concat(isOpen ? 'invisible pointer-events-none' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_MessageSquare_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-6 h-6 animate-spin cursor-pointer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n        lineNumber: 85,\n        columnNumber: 9\n    }, this);\n}\n_s(SidebarNavigation, \"rxUIZL0fF+65ERHSegTMVsVEDJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarNavigation;\nvar _c;\n$RefreshReg$(_c, \"SidebarNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navigation/navigation.tsx\n"));

/***/ })

});