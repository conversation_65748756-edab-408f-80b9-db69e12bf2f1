from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import uuid
from datetime import datetime
from contextlib import asynccontextmanager

from services.document_processor import DocumentProcessor
from services.groq_service import GroqService
from services.database import Database
from models.schemas import ChatRequest, ChatResponse, DocumentResponse, SummaryResponse

# Initialize services
document_processor = DocumentProcessor()
groq_service = GroqService()
database = Database()

@asynccontextmanager
async def lifespan(app_instance: FastAPI):
    """Handle startup and shutdown events"""
    # Startup
    await database.connect()
    yield
    # Shutdown
    await database.disconnect()

# Initialize FastAPI app
app = FastAPI(
    title="Insurance AI API",
    description="AI-powered insurance document analysis and Q&A system",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Insurance AI API is running", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "document_processor": "active",
            "groq_service": "active",
            "database": "connected" if database.is_connected else "disconnected"
        }
    }

@app.post("/api/documents/upload", response_model=List[DocumentResponse])
async def upload_documents(
    files: List[UploadFile] = File(...),
    x_user_id: str = Header(...),
    x_session_id: Optional[str] = Header(None)
):
    """Upload and process insurance documents"""
    if not files:
        raise HTTPException(status_code=400, detail="No files uploaded")
    
    session_id = x_session_id or str(uuid.uuid4())
    processed_documents = []
    
    for file in files:
        try:
            # Validate file type
            if not document_processor.is_supported_file(file.filename):
                continue
            
            # Save uploaded file
            file_path = await document_processor.save_uploaded_file(file, x_user_id, session_id)
            
            # Extract text from document
            extracted_text = await document_processor.extract_text(file_path, file.content_type)
            
            if not extracted_text.strip():
                continue
            
            # Generate summary using Groq
            summary_result = await groq_service.generate_summary(extracted_text)
            key_points = await groq_service.extract_key_points(extracted_text)
            
            # Save to database
            document_data = {
                "id": str(uuid.uuid4()),
                "filename": file.filename,
                "original_name": file.filename,
                "file_path": file_path,
                "extracted_text": extracted_text,
                "summary": summary_result.get("summary", ""),
                "key_points": key_points.get("key_points", []),
                "user_id": x_user_id,
                "session_id": session_id,
                "processing_status": "completed",
                "created_at": datetime.now(),
                "file_size": len(await file.read()),
                "word_count": len(extracted_text.split())
            }
            
            await database.save_document(document_data)
            
            processed_documents.append(DocumentResponse(
                id=document_data["id"],
                filename=document_data["filename"],
                summary=document_data["summary"],
                key_points=document_data["key_points"],
                processing_status=document_data["processing_status"],
                word_count=document_data["word_count"],
                file_size=document_data["file_size"]
            ))
            
        except Exception as e:
            print(f"Error processing file {file.filename}: {str(e)}")
            continue
    
    return processed_documents

@app.post("/api/chat/message", response_model=ChatResponse)
async def chat_message(
    request: ChatRequest,
    x_user_id: str = Header(...),
):
    """Chat with AI about uploaded documents"""
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")
    
    # Get documents for this session
    documents = await database.get_session_documents(request.session_id, x_user_id)
    
    if not documents:
        raise HTTPException(
            status_code=400, 
            detail="No documents found. Please upload documents first."
        )
    
    # Build context from documents
    document_context = "\n\n".join([
        f"Document: {doc['filename']}\nContent: {doc['extracted_text'][:2000]}...\nSummary: {doc['summary']}"
        for doc in documents
    ])
    
    # Get chat history
    chat_history = await database.get_chat_history(request.session_id, x_user_id)
    
    # Get AI response
    ai_response = await groq_service.answer_question(
        request.message,
        document_context,
        chat_history
    )
    
    # Save chat messages
    await database.save_chat_message(request.session_id, x_user_id, "user", request.message)
    await database.save_chat_message(request.session_id, x_user_id, "assistant", ai_response["answer"])
    
    return ChatResponse(
        response=ai_response["answer"],
        session_id=request.session_id,
        tokens_used=ai_response.get("tokens_used", 0),
        documents_count=len(documents)
    )

@app.get("/api/documents/session/{session_id}")
async def get_session_documents(
    session_id: str,
    x_user_id: str = Header(...)
):
    """Get all documents for a session"""
    documents = await database.get_session_documents(session_id, x_user_id)
    return {"documents": documents}

@app.get("/api/chat/history/{session_id}")
async def get_chat_history(
    session_id: str,
    x_user_id: str = Header(...)
):
    """Get chat history for a session"""
    messages = await database.get_chat_history(session_id, x_user_id)
    return {"messages": messages, "session_id": session_id}

@app.post("/api/chat/summarize/{document_id}", response_model=SummaryResponse)
async def generate_document_summary(
    document_id: str,
    x_user_id: str = Header(...)
):
    """Generate or retrieve document summary"""
    document = await database.get_document(document_id, x_user_id)
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    if document.get("summary"):
        return SummaryResponse(
            success=True,
            summary=document["summary"],
            key_points=document.get("key_points", [])
        )
    
    # Generate new summary
    summary_result = await groq_service.generate_summary(document["extracted_text"])
    key_points = await groq_service.extract_key_points(document["extracted_text"])
    
    # Update document
    await database.update_document_summary(
        document_id, 
        summary_result.get("summary", ""),
        key_points.get("key_points", [])
    )
    
    return SummaryResponse(
        success=True,
        summary=summary_result.get("summary", ""),
        key_points=key_points.get("key_points", []),
        tokens_used=summary_result.get("tokens_used", 0) + key_points.get("tokens_used", 0)
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
