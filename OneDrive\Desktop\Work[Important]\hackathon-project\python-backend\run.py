import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"
    
    print(f"🚀 Starting Insurance AI Backend on {host}:{port}")
    print(f"📊 Debug mode: {debug}")
    print(f"🔑 Groq API Key: {'✓ Set' if os.getenv('GROQ_API_KEY') else '✗ Missing'}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
