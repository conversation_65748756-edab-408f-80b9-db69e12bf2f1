"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/ai-input */ \"(app-pages-browser)/./src/components/ui/ai-input.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation/navigation */ \"(app-pages-browser)/./src/components/Navigation/navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Page = ()=>{\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isAiTyping, setIsAiTyping] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const generateChatTitle = (messages)=>{\n        if (messages.length === 0) return \"New Chat\";\n        const firstUserMessage = messages.find((msg)=>!msg.isAi);\n        if (firstUserMessage) {\n            return firstUserMessage.text.length > 30 ? firstUserMessage.text.substring(0, 30) + \"...\" : firstUserMessage.text;\n        }\n        return \"New Chat\";\n    };\n    const handleNewChat = ()=>{\n        // Save current chat to history if it has messages\n        if (messages.length > 0) {\n            const newChat = {\n                id: Date.now().toString(),\n                title: generateChatTitle(messages),\n                messages: [\n                    ...messages\n                ],\n                timestamp: new Date()\n            };\n            setChatHistory((prev)=>[\n                    newChat,\n                    ...prev\n                ]);\n        }\n        // Clear current chat\n        setMessages([]);\n        setCurrentChatId(null);\n        setIsAiTyping(false);\n    };\n    const handleLoadChat = (chat)=>{\n        // Save current chat if it has messages and is different from the one being loaded\n        if (messages.length > 0 && currentChatId !== chat.id) {\n            const currentChat = {\n                id: currentChatId || Date.now().toString(),\n                title: generateChatTitle(messages),\n                messages: [\n                    ...messages\n                ],\n                timestamp: new Date()\n            };\n            setChatHistory((prev)=>{\n                const filtered = prev.filter((c)=>c.id !== currentChatId);\n                return [\n                    currentChat,\n                    ...filtered\n                ];\n            });\n        }\n        // Load selected chat\n        setMessages(chat.messages);\n        setCurrentChatId(chat.id);\n        setIsAiTyping(false);\n    };\n    const handleMessage = async (message)=>{\n        // Add user message\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    text: message,\n                    isAi: false\n                }\n            ]);\n        setIsAiTyping(true);\n        // Simulate AI response (replace this with actual AI integration)\n        setTimeout(()=>{\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        text: \"This is a simulated AI response.\",\n                        isAi: true\n                    }\n                ]);\n            setIsAiTyping(false);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-[250px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation_navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col h-[690px] relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-y-auto px-4 pb-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4 py-4\",\n                            children: [\n                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg max-w-[80%] \".concat(msg.isAi ? 'bg-neutral-100 self-start' : 'bg-[#ff3f17]/15 text-[#ff3f17] self-end'),\n                                        children: msg.text\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                isAiTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-neutral-100 self-start\",\n                                    children: \"AI is typing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-6 left-0 right-0 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ai_input__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onSendMessage: handleMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Page, \"f9toegg2CiUbQzDOs0vKWP6EREs=\");\n_c = Page;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});