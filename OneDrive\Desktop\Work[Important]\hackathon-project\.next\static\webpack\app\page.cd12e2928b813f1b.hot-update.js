"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Navigation/navigation.tsx":
/*!**************************************************!*\
  !*** ./src/components/Navigation/navigation.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SidebarNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,SquareArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,SquareArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SidebarNavigation(param) {\n    let { onNewChat, onLoadChat, chatHistory, currentChatId } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const toggleSidebar = ()=>setIsOpen(true);\n    const closeSidebar = ()=>setIsOpen(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const startNewChat = ()=>{\n        onNewChat();\n        closeSidebar();\n    };\n    const formatTimestamp = (timestamp)=>{\n        const now = new Date();\n        const diff = now.getTime() - timestamp.getTime();\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days === 0) {\n            return 'Today';\n        } else if (days === 1) {\n            return 'Yesterday';\n        } else if (days < 7) {\n            return \"\".concat(days, \" days ago\");\n        } else {\n            return timestamp.toLocaleDateString();\n        }\n    };\n    // Close on outside click\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarNavigation.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {\n                    closeSidebar();\n                }\n            }\n            if (isOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n            } else {\n                document.removeEventListener('mousedown', handleClickOutside);\n            }\n            return ({\n                \"SidebarNavigation.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"SidebarNavigation.useEffect\"];\n        }\n    }[\"SidebarNavigation.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: sidebarRef,\n                className: \"fixed top-0 left-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 \".concat(isOpen ? 'translate-x-0' : '-translate-x-full'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: \"Navigation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeSidebar,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6 ml-45 -mt-15 cursor-pointer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"default\",\n                            className: \"w-full cursor-pointer\",\n                            onClick: startNewChat,\n                            children: \"+ New Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                lineNumber: 87,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleSidebar,\n                className: \"absolute top-4 left-4 z-60 bg-white p-2 rounded-full drop-shadow-lg shadow-black transition-opacity duration-300 \".concat(isOpen ? 'invisible pointer-events-none' : ''),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_SquareArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6 animate-spin cursor-pointer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\hackathon-project\\\\src\\\\components\\\\Navigation\\\\navigation.tsx\",\n        lineNumber: 85,\n        columnNumber: 9\n    }, this);\n}\n_s(SidebarNavigation, \"rxUIZL0fF+65ERHSegTMVsVEDJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarNavigation;\nvar _c;\n$RefreshReg$(_c, \"SidebarNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navigation/navigation.tsx\n"));

/***/ })

});