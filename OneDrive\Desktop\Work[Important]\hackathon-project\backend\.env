# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Groq API Configuration
GROQ_API_KEY=********************************************************

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/insurance-ai

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRES_IN=7d

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
